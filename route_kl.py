import logging
from pathlib import Path
from typing import Tuple, List
# from itertools import pairwise

import numpy as np
import osmnx
import pandas as pd
import osmnx as ox
import geopandas as gpd
from shapely.geometry import Point
from pyproj import Geod


_GEOD = Geod(ellps='WGS84')
logger = logging.getLogger(__name__)
logging.basicConfig(format='%(asctime)s %(levelname)-8s: %(message)s', level=logging.INFO,
                    datefmt='%Y-%m-%d %H:%M:%S')
_PAVED_TYPES = {'motorway', 'trunk', 'primary', 'secondary', 'tertiary', 'unclassified', 'residential', 'primary_link',
                'secondary_link', 'tertiary_link', 'motorway_link', 'trunk_link'}
_OFF_ROAD_TYPES = {'service', 'track', 'escape', 'road', 'footway', 'cycleway', 'path', 'construction', 'proposed',
                   'too_far'}
def pairwise(iterable):
    # pairwise('ABCDEFG') → AB BC CD DE EF FG
    iterator = iter(iterable)
    a = next(iterator, None)
    for b in iterator:
        yield a, b
        a = b


class EmptyTripError(ValueError):
    """Error when the Trip is empty"""


class InvalidTripDataError(ValueError):
    """Error when the Trip is not meeting expectations"""


class InvalidSurfaceTypeError(KeyError):
    """Error when the surface type is missing"""


class NotUniqueSurfaceTypeError(ValueError):
    """Error when multiple surface types are found"""


class _BaseStructure:
    """Abstraction of a structure that contains the raw Azuga data as a DataFrame, expects longitude & latitude"""
    _LATITUDE = 'latitude'
    _LONGITUDE = 'longitude'

    def __init__(self, df: pd.DataFrame):
        self.df = df

    def __len__(self):
        return len(self.df)

    @property
    def latitudes(self) -> np.ndarray:
        return self.df[self._LATITUDE].values

    @property
    def longitudes(self) -> np.ndarray:
        return self.df[self._LONGITUDE].values

    @property
    def timeStamps(self) -> np.ndarray:
        return self.df['timestamp'].values

    @property
    def duration(self) -> float:
        """Returns duration in days"""
        secondsInDays = 3600 * 24
        return (self.df['timestamp'].max() - self.df['timestamp'].min()) / secondsInDays

    @property
    def boundingBox(self) -> Tuple:
        print (np.min(self.longitudes), np.min(self.latitudes),  np.max(self.longitudes), np.max(self.latitudes))
        print ('*-'*50)
        return (np.min(self.longitudes)-0.05, np.min(self.latitudes)-0.05,  np.max(self.longitudes)+0.05, np.max(self.latitudes)+0.05)


    def mapBox(self):
        """Bounding box that is at least 100 km2

        0.1 x 0.1 degs corresponds to 100 km2. So, 0.1 deg is added to longitude & latitude

        :return:
        """
        if self.boundingArea < 1e8:
            return (np.min(self.longitudes) - 0.05, np.min(self.latitudes) - 0.05,
                    np.max(self.longitudes) + 0.05, np.max(self.latitudes) + 0.05)
        else:
            return self.boundingBox

    @property
    def polygon(self) -> Tuple[List]:
        """Creates a polygon with counter clockwise traversal

        :return: longitudes, latitudes
        :rtype: List, List
        """
        (minX, minY, maxX, maxY) = self.boundingBox
        longitudes = [minX, maxX, maxX, minX]
        latitudes = [minY, minY, maxY, maxY]

        return longitudes, latitudes

    @property
    def boundingArea(self) -> float:
        """Bounding box area in m^2

        :return:
        :rtype: float
        """
        return _GEOD.polygon_area_perimeter(*self.polygon)[0]

    @property
    def boundingPerimeter(self) -> float:
        """Bounding box perimeter in m

        :return:
        :rtype: float
        """
        return _GEOD.polygon_area_perimeter(*self.polygon)[1]

class TripVariable:
    def __init__(self, count, mean, variance):
        self.count = count
        self.mean = mean
        self.variance = variance

    def __str__(self):
        return '<Avg = {:.2f} with stdev = {:.2f} from {} points>'.format(self.mean,
                                                                          self.variance,
                                                                          self.count)

    @property
    def stdDev(self):
        return np.sqrt(self.variance)

    @classmethod
    def fromData(cls, array: np.ndarray):
        count = len(array)
        mean = np.average(array)
        variance = np.std(array) ** 2

        return cls(count, mean, variance)

    def addData(self, array: np.ndarray):
        count = len(array)
        if count > 0:
            mean = np.average(array)
            variance = np.std(array) ** 2

            newCount = self.count + count
            newMean = (self.count * self.mean + count * mean) / (self.count + count)
            newVariance = (self.count * (self.variance + (self.mean - newMean) ** 2) +
                           count * (variance + (mean - newMean) ** 2)) / newCount

            self.count = newCount
            self.mean = newMean
            self.variance = newVariance

class _SurfaceSummary:
    """Abstraction of a summary on a particular surface for a vehicle"""
    def __init__(self, serialNumber: str, surfaceType: str, duration: int, distance: float, speedSummary: TripVariable):
        """

        :param str serialNumber:
        :param str surfaceType:
        :param int duration:
        :param float distance:
        :param TripVariable speedSummary:
        """
        self.serialNumber = serialNumber
        self.surfaceType = surfaceType
        self.duration = duration
        self.distance = distance
        self.speed = speedSummary

    def __str__(self):
        return ('<Summary: {}; distance {:.1f} km; duration {:1f} '
                'hrs; speed {}>').format(self.surfaceType, self.distance / 1e3,
                                         self.duration / 3600, self.speed)

    @classmethod
    def fromFrame(cls, serialNumber: str, df: pd.DataFrame, validate=True):
        surfaceTypes = df['highway'].unique()
        if validate and len(surfaceTypes) != 1:
            raise NotUniqueSurfaceTypeError('Got multiple surface types {}'.format(surfaceTypes))

        duration = df['timesteps'].sum()
        distance = df['distances'].sum()
        speed = TripVariable.fromData(df['speed'].values)

        return cls(serialNumber, surfaceTypes[0], duration, distance, speed)

    def addFrom(self, df: pd.DataFrame, validate=True):
        surfaceTypes = df['highway'].unique()
        if validate and len(surfaceTypes) != 1:
            raise NotUniqueSurfaceTypeError('Got multiple surface types {}'.format(surfaceTypes))

        self.duration += df['timesteps'].sum()
        self.distance += df['distances'].sum()

        self.speed.addData(df['speed'].values)


class OverallSummary:
    def __init__(self, offRoad: _SurfaceSummary, paved: _SurfaceSummary):
        self.offRoad = offRoad
        self.paved = paved

    def __str__(self):
        return '<OffRoad: {}; Paved: {}>'.format(self.offRoad, self.paved)

    @property
    def summary(self):
        distance = self.offRoad.distance + self.paved.distance

        return pd.Series({'offroad_per': self.offRoad.distance / distance,
                          'offroad_speed': self.offRoad.speed.mean,
                          'paved_per': self.paved.distance / distance,
                          'paved_speed': self.paved.speed.mean})

    @classmethod
    def fromTrip(cls, trip):
        df = trip.df[trip.df['is_offroad']]
        offRoad = _SurfaceSummary.fromFrame(trip.serialNumber, df, validate=False)

        df = trip.df[~trip.df['is_offroad']]
        paved = _SurfaceSummary.fromFrame(trip.serialNumber, df, validate=False)

        return cls(offRoad, paved)

    def addTrip(self, trip):
        df = trip.df[trip.df['is_offroad']]
        self.offRoad.addFrom(df, validate=False)

        df = trip.df[~trip.df['is_offroad']]
        self.paved.addFrom(df, validate=False)


class SurfaceSummaries:
    """Abstraction of a list of summaries on various highway surfaces"""
    def __init__(self, summaries: List[_SurfaceSummary], overall: OverallSummary):
        self.summaries = summaries
        self.overall = overall

    def __len__(self):
        return len(self.summaries)

    def __iter__(self):
        return iter(self.summaries)

    def __getitem__(self, item):
        for summary in self:
            if summary.surfaceType == item:
                result = summary
                break
        else:
            raise InvalidSurfaceTypeError('{} is not in {}'.format(item, self.surfaceTypes))

        return result

    @property
    def summary(self) -> pd.Series:
        result = self.overall.summary

        for item in self:
            result['{}_dist_km'.format(item.surfaceType)] = item.distance / 1e3
            result['{}_speed_kph'.format(item.surfaceType)] = item.speed.mean

        return result

    @property
    def surfaceTypes(self) -> List[str]:
        return [summary.surfaceType for summary in self]

    @property
    def serialNumber(self) -> str:
        return self.summaries[0].serialNumber

    @classmethod
    def fromTrip(cls, serialNumber: str, trip):
        """

        :param str serialNumber:
        :param Trip trip:

        :return:
        :rtype: SurfaceSummaries
        """
        surfaceTypes = trip.df['highway'].unique()
        summaries = []
        for surface in surfaceTypes:
            surfaceFrame = trip.df[trip.df['highway'] == surface]
            summaries.append(_SurfaceSummary.fromFrame(serialNumber, surfaceFrame))

        overall = OverallSummary.fromTrip(trip)

        return cls(summaries, overall)

    def addTrip(self, trip):
        """

        :param Trip trip:

        :rtype: None
        """
        surfaceTypes = trip.df['highway'].unique()
        for surface in surfaceTypes:
            surfaceFrame = trip.df[trip.df['highway'] == surface]
            try:
                summary = self[surface]
            except InvalidSurfaceTypeError:
                self.summaries.append(_SurfaceSummary.fromFrame(self.serialNumber,
                                                                surfaceFrame))
            else:
                summary.addFrom(surfaceFrame)

        self.overall.addTrip(trip)


class _FullVehicleHistory(_BaseStructure):
    """Abstraction of the total history of a vehicle"""

    def __init__(self, serialNumber: str, df: pd.DataFrame):
        super().__init__(df)
        self.serialNumber = serialNumber
        logger.info(' created full history for {}'.format(serialNumber))

    @classmethod
    def fromPath(cls, path: Path):
        """Read history from the full data set for a vehicle

        :param Path path:

        :return:
        :rtype: _FullVehicleHistory
        """
        def _number(filePath):
            return int(filePath.name.replace('{}_'.format(serialNumber), '').replace('.csv', ''))

        serialNumber = path.name
        logger.info('=== Reading data for Vehicle {} ==='.format(serialNumber))
        files = sorted(path.glob('{}_*.csv'.format(serialNumber)), key=_number)

        frames = []
        for index, file in enumerate(files):
            logger.info('  reading {:3d}/{:3d} - file {}'.format(index + 1, len(files), file.name))
            frame = pd.read_csv(file, header=0, usecols=[2, 3, 4, 5, 6, 7, 8], dtype={'timestamp': np.int32})
            frames.append(frame)

        return cls(serialNumber, pd.concat(frames))


class VehicleHistory(_FullVehicleHistory):
    """Abstraction where data is converted to 1Hz"""

    def __init__(self, serialNumber: str, df: pd.DataFrame):
        """
        ..note: Do not use this method. Use constructors instead or write new ones
        :param serialNumber:
        :param df:
        """
        super().__init__(serialNumber, df)
        logger.info(' created incremental history for {}'.format(serialNumber))

        self.df.loc[:, 'geometry'] = self.df.apply(lambda row: Point(row[_BaseStructure._LONGITUDE],
                                                                     row[_BaseStructure._LATITUDE]),
                                                   axis=1)
        self.df.loc[:, 'human_time'] = pd.to_datetime(self.df['timestamp'], utc=True, unit='s')
        logger.info(' created Shapely Point geometries')

    @property
    def startTime(self):
        return self.df.loc[0, 'human_time']

    @property
    def stopTime(self):
        return self.df.loc[self.df.index[-1], 'human_time']

    @property
    def distances(self) -> np.ndarray:
        return self.df['distances'].values

    @property
    def timeSteps(self) -> np.ndarray:
        return self.df['timesteps'].values

    @staticmethod
    def _addDistances(df: pd.DataFrame) -> np.ndarray:
        # add distances between points
        longitudes = df[_BaseStructure._LONGITUDE].values
        latitudes = df[_BaseStructure._LATITUDE].values

        return _GEOD.inv(longitudes[:-1], latitudes[:-1], longitudes[1:], latitudes[1:])[2]

    def _findNearest(self) -> gpd.GeoDataFrame:
        """Find nearest highway type from sjoin_nearest. Can have multiple result for each time

        :return:
        """
        def _cleanUp(x):
            """Cleans up when highway entry is a list - if any offroad exists, use one of those, else use first item"""
            if isinstance(x, list):
                x_ = set(x)
                anyOffRoad = _OFF_ROAD_TYPES.intersection(x_)
                if anyOffRoad:
                    return anyOffRoad.pop()
                else:
                    return x[0]
            else:
                return x
        # find the map and associated graph
        graph = ox.graph_from_bbox(self.mapBox(), network_type='all', simplify=True,
                                   retain_all='True', truncate_by_edge=False)
        logger.info(' created graph')
        graph = osmnx.project_graph(graph, to_crs=5071)
        logger.info(' projected graph from geographic to epsg 5071 ')
        nodes, edges = ox.graph_to_gdfs(graph)
        logger.info(' created edges for map of area {:.2f} km2'.format(self.boundingArea / 1e6))

        # find projected geo dataframe
        gdf_ = self.df.loc[:, ['timestamp', 'geometry']]
        gdfEllipsoid = gpd.GeoDataFrame(gdf_, crs='EPSG:4326')
        gdfProjected = gdfEllipsoid.to_crs(epsg=5071)
        logger.info(' created GeoDataFrame in ellipsoid & projected space')

        nearest = gpd.sjoin_nearest(gdfProjected, edges[['geometry', 'highway']],
                                    distance_col='closeness')
        logger.info(' found closest highways')
        nearest['cleaned_hwy'] = nearest['highway'].apply(_cleanUp)
        logger.info(' cleaned up highways')

        return nearest

    def _findHighway(self):
        """Adds nearest highway, hwy_count and closest columns to the dataFrame

        :return:
        """
        def _isTooFar(x):
            """if the closest highway is more than 15 m, then assign as too_far"""
            if x['closest'] > 30:
                return 'too_far'
            else:
                return x['highway']

        def _isOffRoad(x):
            return x in _OFF_ROAD_TYPES

        nearest = self._findNearest()
        # check whether there are multiple type of highway at each time stamp
        grouped = nearest.groupby(by='timestamp')
        logger.info(' find grouped by timestamp')
        result = grouped.agg(hwy_count=pd.NamedAgg(column='cleaned_hwy', aggfunc='nunique'),
                             closest=pd.NamedAgg(column='closeness', aggfunc='min'),
                             highway=pd.NamedAgg(column='cleaned_hwy', aggfunc='first')).reset_index()
        logger.info(' find unique highway in each group')

        self.df = pd.merge(self.df, result, on='timestamp', how='left')
        self.df['highway'] = self.df.apply(_isTooFar, axis=1)
        self.df['is_offroad'] = self.df['highway'].apply(_isOffRoad)
        logger.info(' mapped closest highways to timestamp')

    def write(self, path: Path):
        """Writes output to csv file

        :param path:
        :return:
        """
        columnsToWrite = ['timestamp', 'speed', 'latitude', 'longitude', 'ax', 'ay', 'az',
                          'timesteps', 'distances', 'hwy_count', 'closest', 'highway', 'is_offroad']
        # self.df.to_csv(path, header=True, index=False, mode='w', lineterminator='\n',
                    #    columns=columnsToWrite)
        self.df.to_csv(path, header=True, index=False, mode='w', lineterminator='\n')

    def drawMap(self, path: Path):
        """Draws the map of roads & region

        :param path:
        :return:
        """
        gdf_ = self.df.loc[:, ['human_time', 'geometry', 'speed', 'distances', self._LATITUDE, self._LONGITUDE, 'is_offroad', 'highway', 'closest']]
        gdfEllipsoid = gpd.GeoDataFrame(gdf_, crs='EPSG:4326')
        projected = gdfEllipsoid.to_crs(epsg=5071)

        interactive = projected.explore(column='is_offroad', cmap='cool', tooltip=True)
        roadPath = path / '{}-off-road-openstreet.html'.format(self.serialNumber)
        interactive.save(roadPath)
        logger.info(' === wrote {} off-road map to {}'.format(self.serialNumber, roadPath))

        gdf_ = self.df.loc[:, ['geometry', 'is_offroad']]
        gdfEllipsoid = gpd.GeoDataFrame(gdf_, crs='EPSG:4326')
        projected = gdfEllipsoid.to_crs(epsg=5071)

        interactive = projected.explore(column='is_offroad', cmap='cool', tooltip=False, tiles="CartoDB positron", highlight=False)
        roadPath = path / '{}-off-road-carto.html'.format(self.serialNumber)
        interactive.save(roadPath)
        logger.info(' === wrote {} off-road map to {}'.format(self.serialNumber, roadPath))

    @classmethod
    def fromSummaryPath(cls, serialNumber: str, path):
        """Reads from summary csv that is already written

        :return:
        """
        df = pd.read_csv(path, header=0, dtype={'timestamp': np.int32})
        logger.info(' === reading {} from {}'.format(serialNumber, path))

        return cls(serialNumber, df)

    @classmethod
    def fromQuery(cls, serialNumber):
        """Reads the history from a serial number

        """
        from pyspark.sql import SparkSession
        from pyspark.sql.functions import col, unix_timestamp
        from pyspark.sql.types import FloatType

        spark = SparkSession.builder.appName("Spark DataFrames").getOrCreate()
        df = spark.sql(f"""
                       SELECT vdir.vehicleid, vdir.starttime, vdir.speed, vdir.longitude, vdir.latitude
                       FROM tirematics_sandbox.engine.vehicledirection as vdir
                       WHERE vdir.vehicleid={serialNumber} and vdir.speed < 200 and vdir.longitude is not NULL and vdir.latitude is not NULL""")
        logger.info(' Completed query; columns are {}'.format(df.columns))

        data = df.select(col('vehicleid').alias('serialNumber'),
                        unix_timestamp(col('starttime')).alias('timestamp'),
                        col('speed').alias('speed'),
                        col('vdir.longitude').cast(FloatType()),
                        col('vdir.latitude').cast(FloatType()))
        pdf = data.toPandas()

        path = Path('/Volumes/tm_sandbox_catalog/research_and_development/varghese_a/route_analysis/results')
        median = pdf.groupby('timestamp').median().reset_index()
        logger.info(' Converted to pandas; columns are {}'.format(median.columns))
        outputPath = path / '{}-raw.csv'.format(serialNumber)
        median.to_csv(outputPath, header=True, index=False, mode='w', lineterminator='\n')

        # converts to increments
        shifted = median.shift(-1)
        increment = 0.5 * ((median + shifted)[:-1])
        increment['timestamp'] = median['timestamp'].iloc[:-1].astype(int)  # timestamp is start
        increment['timesteps'] = (shifted['timestamp'] - median['timestamp'])[:-1].astype(int)
        logger.info(' converted the data to increments')
        increment['distances'] = cls._addDistances(median)
        logger.info(' added incremental distances')
        filtered = increment[increment['distances'] > 0].copy()
        logger.info(' removed rows without distances')

        result = cls(serialNumber, filtered)
        result._findHighway()

        outputPath = path / '{}-history.csv'.format(result.serialNumber)
        result.write(outputPath)
        logger.info(' === wrote {} vehicle history to {}'.format(result.serialNumber, outputPath))
        result.drawMap(path)

        return result


    @classmethod
    def fromPseudoQuery(cls, serialNumber):
        """Reads the history from a serial number
        """
        # from pyspark.sql import SparkSession
        # from pyspark.sql.functions import col, unix_timestamp
        # from pyspark.sql.types import FloatType

        # spark = SparkSession.builder.appName("Spark DataFrames").getOrCreate()
        # df = spark.sql(f"""
        #                SELECT vdir.vehicleid, vdir.starttime, vdir.speed, vdir.longitude, vdir.latitude
        #                FROM tirematics_sandbox.engine.vehicledirection as vdir
        #                WHERE vdir.vehicleid={serialNumber} and vdir.speed < 200 and vdir.longitude is not NULL and vdir.latitude is not NULL""")
        # logger.info(' Completed query; columns are {}'.format(df.columns))

        # data = df.select(col('vehicleid').alias('serialNumber'),
        #                 unix_timestamp(col('starttime')).alias('timestamp'),
        #                 col('speed').alias('speed'),
        #                 col('vdir.longitude').cast(FloatType()),
        #                 col('vdir.latitude').cast(FloatType()))
        # pdf = data.toPandas()

        path = Path(r'D:\_2025_work\D2EC_2025-08-07_osmnx')
        # median = pdf.groupby('timestamp').median().reset_index()
        # logger.info(' Converted to pandas; columns are {}'.format(median.columns))
        # outputPath = path / '{}-raw.csv'.format(serialNumber)
        # median.to_csv(outputPath, header=True, index=False, mode='w', lineterminator='\n')
        csvpath=r'D:\_2025_work\D2EC_2025-08-07_osmnx\1FBAX2C81LKA02107_raw.csv'
        csvpath=r'D:\_2025_work\D2EC_2025-08-07_osmnx\3AKJHPDV8NSNP0374_geotab.csv'
        csvpath=r'D:\_2025_work\D2EC_2025-08-07_osmnx\G90A21219FAD-2023-11-13.csv'


        ddf=pd.read_csv(csvpath,parse_dates=['datetime'])
        ddf.columns=ddf.columns.str.lower()
        ddf['timestamp']=ddf['datetime'].map(pd.Timestamp.timestamp).astype(int)
        # 2025-08-07 11:18:51 INFO    :  created Shapely Point geometries
        pdf=ddf[['timestamp','latitude','longitude','speed']]
        median = pdf.groupby('timestamp').median().reset_index()
        # converts to increments
        shifted = median.shift(-1)
        increment = 0.5 * ((median + shifted)[:-1])
        increment['timestamp'] = median['timestamp'].iloc[:-1].astype(int)  # timestamp is start
        increment['timesteps'] = (shifted['timestamp'] - median['timestamp'])[:-1].astype(int)
        logger.info(' converted the data to increments')
        increment['distances'] = cls._addDistances(median)
        logger.info(' added incremental distances')
        filtered = increment[increment['distances'] > 0].copy()
        logger.info(' removed rows without distances')

        result = cls(serialNumber, filtered)
        result._findHighway()

        outputPath = path / '{}-history.csv'.format(result.serialNumber)
        print (outputPath,'=='*20)
        result.write(outputPath)
        logger.info(' === wrote {} vehicle history to {}'.format(result.serialNumber, outputPath))
        result.drawMap(path)

        return result



    @classmethod
    def fromPath(cls, path: Path):
        """Read history from the full data set for a vehicle

        :param Path path:

        :return:
        :rtype: VehicleHistory
        """
        fullHistory = _FullVehicleHistory.fromPath(path)

        averaged = fullHistory.df.groupby('timestamp').median().reset_index()
        logger.info(' converted the data from 24Hz to 1Hz')

        # converts to increments
        shifted = averaged.shift(-1)
        increment = 0.5 * ((averaged + shifted)[:-1])
        increment['timestamp'] = averaged['timestamp'].iloc[:-1].astype(int)  # timestamp is start
        increment['timesteps'] = (shifted['timestamp'] - averaged['timestamp'])[:-1].astype(int)
        logger.info(' converted the data to increments')
        increment['distances'] = cls._addDistances(averaged)
        logger.info(' added incremental distances')
        filtered = increment[increment['distances'] > 0].copy()
        logger.info(' removed rows without distances')

        result = cls(fullHistory.serialNumber, filtered)
        result._findHighway()

        outputPath = path / '{}-history.csv'.format(result.serialNumber)
        result.write(outputPath)
        logger.info(' === wrote {} vehicle history to {}'.format(result.serialNumber, outputPath))
        result.drawMap(path)
        return result

class TripVariable:
    def __init__(self, count, mean, variance):
        self.count = count
        self.mean = mean
        self.variance = variance

    def __str__(self):
        return '<Avg = {:.2f} with stdev = {:.2f} from {} points>'.format(self.mean,
                                                                          self.variance,
                                                                          self.count)

    @property
    def stdDev(self):
        return np.sqrt(self.variance)

    @classmethod
    def fromData(cls, array: np.ndarray):
        count = len(array)
        mean = np.average(array)
        variance = np.std(array) ** 2

        return cls(count, mean, variance)

    def addData(self, array: np.ndarray):
        count = len(array)
        if count > 0:
            mean = np.average(array)
            variance = np.std(array) ** 2

            newCount = self.count + count
            newMean = (self.count * self.mean + count * mean) / (self.count + count)
            newVariance = (self.count * (self.variance + (self.mean - newMean) ** 2) +
                           count * (variance + (mean - newMean) ** 2)) / newCount

            self.count = newCount
            self.mean = newMean
            self.variance = newVariance

class _TripInstant(_BaseStructure):
    """Abstraction of a Trip; Raw data separated by time stamps of more than 10 seconds. Each row of DataFrame corresponds
    to a point in the data file or an instant in time;"""

    def __init__(self, serialNumber: str, df: pd.DataFrame):
        super().__init__(df)
        self.serialNumber = serialNumber

    def __str__(self):
        return '<TripInstant: from {} to {} with {} points>'.format(self.timeStamps[0],
                                                                    self.timeStamps[-1],
                                                                    len(self))

    @classmethod
    def fromHistory(cls, history: _FullVehicleHistory):
        """Reads data from folder

        :param history:

        :return: list of TripInstants
        :rtype: list[_TripInstant]
        """
        result = []
        timeStamps = history.df['timestamp'].values
        tripSplits = np.nonzero(np.abs(np.diff(timeStamps)) > 1)[0] + 1
        indices = np.concatenate(([0], tripSplits, [len(timeStamps) - 1]))
        for begin, end in pairwise(indices):
            result.append(_TripInstant(history.serialNumber, history.df.iloc[begin: end]))
        logger.info('Found {} trips'.format(len(result)))

        return result


class Trip(_BaseStructure):
    """Abstraction of a trip; Time is continuous; Each row corresponds to an increment / duration of time; contains nearest
    road type. Data is reduced from 24 Hz to 1Hz.

    TimeStamp corresponds to start of an increment.

    New variables - timesteps, distances, highway, closest
    """
    def __init__(self, serialNumber: str, df: pd.DataFrame):
        """
        ..note: DO NOT USE this constructor; highway will not added using this constructor

        :param serialNumber:
        :param df:
        """
        super().__init__(df)
        self.serialNumber = serialNumber

    def __str__(self):
        return '<Trip: {:.2f} km in {:.2f} hrs; max increment: {:.2f}m>'.format(self.distance / 1000,
                                                                                self.duration / 3600,
                                                                                np.max(self.distances))

    @property
    def distances(self) -> np.ndarray:
        return self.df['distances'].values

    @property
    def distance(self) -> float:
        return np.sum(self.distances)

    @property
    def timeSteps(self) -> np.ndarray:
        return self.df['timesteps'].values

    @property
    def duration(self) -> int:
        return np.sum(self.timeSteps)

    @classmethod
    def fromHistory(cls, history: VehicleHistory):
        """Create from a history. this is the main constructor for trips

        :param VehicleHistory history:

        :return:
        :rtype: List[Trip]
        """
        timeStamps = history.timeStamps
        timeSplits = np.nonzero(np.diff(timeStamps) > 10)[0] + 1
        indices = np.concatenate(([0], timeSplits, [len(timeStamps) - 1]))

        result = []
        for begin, end in pairwise(indices):
            chunk = history.df.iloc[begin: end - 1]
            result.append(cls(history.serialNumber, chunk))

        return result


class VehicleSummary:
    def __init__(self, serialNumber, history: VehicleHistory, trips: List[Trip], summaries: SurfaceSummaries):
        self.serialNumber = serialNumber
        self.history = history
        self.trips = trips
        self.summaries = summaries

    @property
    def summary(self):
        """

        """
        result = pd.Series({'start': self.history.startTime,
                            'stop': self.history.stopTime,
                            'duration_days': self.history.duration,
                            'distance_km': sum(trip.distance for trip in self.trips) / 1e3})

        return pd.concat([result, self.summaries.summary])

    @classmethod
    def fromSummaryPath(cls, path: Path):
        """Reads data from summary file"""
        serialNumber = path.parent.name
        history = VehicleHistory.fromSummaryPath(serialNumber, path)
        trips = Trip.fromHistory(history)
        summaries = SurfaceSummaries.fromTrip(serialNumber, trips[0])

        for trip in trips[1:]:
            summaries.addTrip(trip)

        return cls(serialNumber, history, trips, summaries)

    @classmethod
    def fromQuery(cls, serialNumber: int):
        """Reads data from query"""
        history = VehicleHistory.fromQuery(serialNumber)
        trips = Trip.fromHistory(history)
        summaries = SurfaceSummaries.fromTrip(serialNumber, trips[0])

        for trip in trips[1:]:
            summaries.addTrip(trip)

        return cls(serialNumber, history, trips, summaries)

    @classmethod
    def fromPath(cls, path: Path):
        """Reads data from folder

        :param path:

        :return:
        """
        history = VehicleHistory.fromPath(path)
        trips = Trip.fromHistory(history)
        summaries = SurfaceSummaries.fromTrip(history.serialNumber, trips[0])

        for trip in trips[1:]:
            summaries.addTrip(trip)

        return cls(history.serialNumber, history, trips, summaries)

def drawRoutes(self, path: Path):
    pass

if __name__== '__main__':
# D:\_2025_work\D2EC_2025-08-07_osmnx\3AKJHPDV8NSNP0374_geotab.csv
    VehicleHistory.fromPseudoQuery('G90A21219FAD')
    # pass